<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">Service Order Details</h3>
          <div class="flex items-center space-x-2">
            <button
              v-if="serviceOrder"
              @click="$emit('edit', serviceOrder)"
              class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Edit
            </button>
            <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div v-if="serviceOrder" class="space-y-6">
          <!-- Basic Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Basic Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Serial Number</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.serialNumber || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Service Order No</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.serviceOrderNo || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Customer</label>
                <p class="mt-1 text-sm text-gray-900">{{ getCustomerName(serviceOrder.customer) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Invoice</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.invoice || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <!-- Work Details -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Work Details</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Nature of Work</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.workDetails?.natureOfWork || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Installer</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.workDetails?.installer || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Start Time</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(serviceOrder.workDetails?.startTime) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">End Time</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(serviceOrder.workDetails?.endTime) }}</p>
              </div>
            </div>
            <div class="mt-4" v-if="serviceOrder.workDetails?.tests?.length">
              <label class="block text-sm font-medium text-gray-700 mb-2">Tests Performed</label>
              <div class="flex flex-wrap gap-2">
                <span
                  v-for="test in serviceOrder.workDetails.tests"
                  :key="test"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {{ test }}
                </span>
              </div>
            </div>
          </div>

          <!-- Test Results -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Test Results</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">LAN Speed Test</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.testResults?.lanSpeedTest || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">WiFi 2.4GHz Speed Test</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.testResults?.wifi24GHzSpeedTest || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">WiFi 5GHz Speed Test</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.testResults?.wifi5GHzSpeedTest || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">WiFi Coverage Speed Test</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.testResults?.wifiCoverageSpeedTest || 'N/A' }}</p>
              </div>
            </div>

            <!-- Device APs -->
            <div v-if="serviceOrder.testResults?.deviceAps?.length" class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Device Access Points</label>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-100">
                    <tr>
                      <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Serial Number</th>
                      <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Location</th>
                      <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Signal Strength</th>
                      <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">LAN Speed</th>
                      <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">WiFi Speed</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="ap in serviceOrder.testResults.deviceAps" :key="ap.serialNumber">
                      <td class="px-3 py-2 text-sm text-gray-900">{{ ap.serialNumber }}</td>
                      <td class="px-3 py-2 text-sm text-gray-900">{{ ap.location }}</td>
                      <td class="px-3 py-2 text-sm text-gray-900">{{ ap.signalStrength }}</td>
                      <td class="px-3 py-2 text-sm text-gray-900">{{ ap.lanSpeedTest }}</td>
                      <td class="px-3 py-2 text-sm text-gray-900">{{ ap.wifiSpeedTest }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Connectors -->
            <div v-if="serviceOrder.testResults?.connectors?.length" class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Connectors</label>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-100">
                    <tr>
                      <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Model</th>
                      <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                      <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Quantity</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="(connector, index) in serviceOrder.testResults.connectors" :key="index">
                      <td class="px-3 py-2 text-sm text-gray-900">{{ connector.model }}</td>
                      <td class="px-3 py-2 text-sm text-gray-900">{{ connector.type }}</td>
                      <td class="px-3 py-2 text-sm text-gray-900">{{ connector.quantity }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Cabling Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Cabling Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Outdoor Length</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.cabling?.outdoorLength || 0 }} m</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Indoor Length</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.cabling?.indoorLength || 0 }} m</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Splitter Details</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.cabling?.splitterDetails || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Tagging Details</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.cabling?.taggingDetails || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <!-- Installation Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Installation Information</h4>
            <div class="grid grid-cols-1 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Total Amount</label>
                <p class="mt-1 text-lg font-semibold text-gray-900">RM {{ serviceOrder.installation?.totalAmount?.toFixed(2) || '0.00' }}</p>
              </div>
            </div>

            <!-- Installation Items -->
            <div v-if="hasInstallationItems" class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Installation Items</label>
              <div class="space-y-4">
                <!-- Standard Items -->
                <div v-if="serviceOrder.installation?.standardItems?.length">
                  <h5 class="text-sm font-medium text-gray-600 mb-2">Standard Items</h5>
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-100">
                        <tr>
                          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Unit</th>
                          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Quantity</th>
                          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Rate</th>
                          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Cost</th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="(item, index) in serviceOrder.installation.standardItems" :key="index">
                          <td class="px-3 py-2 text-sm text-gray-900">{{ item.description }}</td>
                          <td class="px-3 py-2 text-sm text-gray-900">{{ item.unit }}</td>
                          <td class="px-3 py-2 text-sm text-gray-900">{{ item.quantity }}</td>
                          <td class="px-3 py-2 text-sm text-gray-900">RM {{ item.rate.toFixed(2) }}</td>
                          <td class="px-3 py-2 text-sm text-gray-900">RM {{ item.cost.toFixed(2) }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Additional Items -->
                <div v-if="serviceOrder.installation?.additionalItems?.length">
                  <h5 class="text-sm font-medium text-gray-600 mb-2">Additional Items</h5>
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-100">
                        <tr>
                          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Unit</th>
                          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Quantity</th>
                          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Rate</th>
                          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Cost</th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="(item, index) in serviceOrder.installation.additionalItems" :key="index">
                          <td class="px-3 py-2 text-sm text-gray-900">{{ item.description }}</td>
                          <td class="px-3 py-2 text-sm text-gray-900">{{ item.unit }}</td>
                          <td class="px-3 py-2 text-sm text-gray-900">{{ item.quantity }}</td>
                          <td class="px-3 py-2 text-sm text-gray-900">RM {{ item.rate.toFixed(2) }}</td>
                          <td class="px-3 py-2 text-sm text-gray-900">RM {{ item.cost.toFixed(2) }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Customer Declaration -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Customer Declaration</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Confirmed By</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.declaration?.confirmedBy || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">MyKad No</label>
                <p class="mt-1 text-sm text-gray-900">{{ serviceOrder.declaration?.myKadNo || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Date</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(serviceOrder.declaration?.date) }}</p>
              </div>
            </div>
          </div>

          <!-- Remarks -->
          <div v-if="serviceOrder.remarks" class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Remarks</h4>
            <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ serviceOrder.remarks }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { CustomerService } from '@/services/api'
import type { ServiceOrder, Customer } from '@/types'

// Props
interface Props {
  serviceOrder?: ServiceOrder | null
}

const props = withDefaults(defineProps<Props>(), {
  serviceOrder: null
})

// Emits
const emit = defineEmits<{
  close: []
  edit: [serviceOrder: ServiceOrder]
}>()

// Reactive data
const customers = ref<Customer[]>([])

// Computed properties
const hasInstallationItems = computed(() => {
  if (!props.serviceOrder?.installation) return false

  const { standardItems, additionalItems, nonStandard } = props.serviceOrder.installation
  return (standardItems && standardItems.length > 0) ||
         (additionalItems && additionalItems.length > 0) ||
         (nonStandard && nonStandard.length > 0)
})

// Load customers
async function loadCustomers() {
  try {
    const response = await CustomerService.getCustomers({ limit: 1000 })
    if (response.success && response.data?.data) {
      customers.value = response.data.data.data || []
    }
  } catch (error) {
    console.error('Error loading customers:', error)
  }
}

// Get customer name by ID
function getCustomerName(customerId: string): string {
  const customer = customers.value.find(c => c.id === customerId)
  return customer?.name || 'Unknown Customer'
}

// Format date time
function formatDateTime(dateString?: string): string {
  if (!dateString) return 'N/A'

  try {
    const date = new Date(dateString)
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  } catch (error) {
    return 'Invalid Date'
  }
}

// Initialize
onMounted(() => {
  loadCustomers()
})
</script>
