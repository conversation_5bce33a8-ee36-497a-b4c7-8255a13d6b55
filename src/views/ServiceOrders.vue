<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Service Orders</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Service Order
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 md:p-6 lg:p-8 rounded-lg shadow">
      <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search service orders..."
            @keyup.enter="handleSearch"
            class="w-full px-3 md:px-4 lg:px-5 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div class="flex gap-2">
          <select
            v-model="statusFilter"
            @change="handleSearch"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Status</option>
            <option value="draft">Draft</option>
            <option value="submitted">Submitted</option>
            <option value="approved">Approved</option>
            <option value="completed">Completed</option>
          </select>
          <button
            @click="clearSearch"
            class="px-4 md:px-6 lg:px-8 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Clear
          </button>
        </div>
      </div>
    </div>

    <!-- Service Orders Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-base font-medium text-gray-900">
          All Service Orders ({{ pagination.state.total }})
        </h3>
      </div>

      <div v-if="loading" class="p-6 text-center">
        <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading...
        </div>
      </div>

      <div v-else-if="serviceOrders.length === 0" class="p-6 text-center text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h4.125M8.25 8.25V6.108" />
        </svg>
        <p class="mt-2">No service orders found</p>
        <p class="text-sm text-gray-400">Get started by creating your first service order</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Service Order
              </th>
              <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Installer
              </th>
              <th class="hidden lg:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Work Details
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="(serviceOrder, index) in serviceOrders"
              :key="index"
              class="hover:bg-gray-50 transition-colors"
            >
              <!-- Service Order Info -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h4.125M8.25 8.25V6.108" />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <div class="text-xs sm:text-sm font-medium text-gray-900">{{ serviceOrder.serviceOrderNo || 'N/A' }}</div>
                    <div class="text-xs sm:text-sm text-gray-500">{{ serviceOrder.serialNumber || 'N/A' }}</div>
                  </div>
                </div>
              </td>

              <!-- Customer -->
              <td class="hidden sm:table-cell px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ getCustomerName(serviceOrder.customer) }}</div>
              </td>

              <!-- Installer -->
              <td class="hidden md:table-cell px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ serviceOrder.workDetails?.installer || 'N/A' }}</div>
              </td>

              <!-- Work Details -->
              <td class="hidden lg:table-cell px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ serviceOrder.workDetails?.natureOfWork || 'N/A' }}</div>
                <div class="text-sm text-gray-500">
                  {{ formatDateRange(serviceOrder.workDetails?.startTime, serviceOrder.workDetails?.endTime) }}
                </div>
              </td>

              <!-- Status -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusBadgeClass(getServiceOrderStatus(serviceOrder))" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getServiceOrderStatus(serviceOrder) }}
                </span>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="viewServiceOrder(serviceOrder)"
                    class="text-blue-600 hover:text-blue-900 transition-colors"
                    title="View Details"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    @click="editServiceOrder(serviceOrder)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors"
                    title="Edit"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="downloadPDF(serviceOrder)"
                    class="text-green-600 hover:text-green-900 transition-colors"
                    title="Download PDF"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteServiceOrder(serviceOrder)"
                    class="text-red-600 hover:text-red-900 transition-colors"
                    title="Delete"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="serviceOrders.length > 0" class="px-6 py-4 border-t border-gray-200">
        <Pagination
          :current-page="pagination.state.currentPage"
          :page-size="pagination.state.pageSize"
          :total="pagination.state.total"
          @page-change="pagination.goToPage"
          @page-size-change="pagination.changePageSize"
        />
      </div>
    </div>

    <!-- Service Order Modal -->
    <ServiceOrderModal
      v-if="showAddModal || showEditModal"
      :service-order="selectedServiceOrder"
      :is-edit-mode="showEditModal"
      @close="closeModal"
      @saved="handleServiceOrderSaved"
    />

    <!-- Service Order Detail Modal -->
    <ServiceOrderDetailModal
      v-if="showDetailModal"
      :service-order="selectedServiceOrder"
      @close="showDetailModal = false"
      @edit="editServiceOrder"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ServiceOrderService, CustomerService } from '@/services/api'
import { usePagination } from '@/composables/usePagination'
import type { ServiceOrder, Customer } from '@/types'
import Pagination from '@/components/Pagination.vue'
import ServiceOrderModal from '@/components/modals/ServiceOrderModal.vue'
import ServiceOrderDetailModal from '@/components/modals/ServiceOrderDetailModal.vue'

// Reactive data
const serviceOrders = ref<ServiceOrder[]>([])
const customers = ref<Customer[]>([])
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')

// Modal states
const showAddModal = ref(false)
const showEditModal = ref(false)
const showDetailModal = ref(false)
const selectedServiceOrder = ref<ServiceOrder | null>(null)

// Pagination
const pagination = usePagination({
  initialPageSize: 10,
  onPageChange: loadServiceOrders
})

// Load service orders
async function loadServiceOrders() {
  loading.value = true
  try {
    const params = pagination.getQueryParamsWithSearch(searchQuery.value)
    if (statusFilter.value) {
      params.status = statusFilter.value
    }

    const response = await ServiceOrderService.getServiceOrders(params)
    if (response.success && response.data?.data) {
      serviceOrders.value = response.data.data.data || []
      pagination.updateFromResponse(response.data.data)
    } else {
      console.error('Failed to load service orders:', response.error)
      serviceOrders.value = []
    }
  } catch (error) {
    console.error('Error loading service orders:', error)
    serviceOrders.value = []
  } finally {
    loading.value = false
  }
}

// Load customers for display
async function loadCustomers() {
  try {
    const response = await CustomerService.getCustomers({ limit: 10 })
    if (response.success && response.data?.data) {
      customers.value = response.data.data.data || []
    }
  } catch (error) {
    console.error('Error loading customers:', error)
  }
}

// Get customer name by ID
function getCustomerName(customerId: string): string {
  const customer = customers.value.find(c => c.id === customerId)
  return customer?.fullName || 'Unknown Customer'
}

// Get service order status
function getServiceOrderStatus(serviceOrder: ServiceOrder): string {
  // This is a simplified status logic - you may want to implement more complex logic
  if (serviceOrder.declaration?.date) {
    return 'Completed'
  } else if (serviceOrder.workDetails?.endTime) {
    return 'In Progress'
  } else {
    return 'Draft'
  }
}

// Get status badge class
function getStatusBadgeClass(status: string): string {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800'
    case 'in progress':
      return 'bg-yellow-100 text-yellow-800'
    case 'approved':
      return 'bg-blue-100 text-blue-800'
    case 'submitted':
      return 'bg-purple-100 text-purple-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Format date range
function formatDateRange(startTime?: string, endTime?: string): string {
  if (!startTime) return 'Not scheduled'

  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : null

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (end) {
    return `${formatDate(start)} - ${formatDate(end)}`
  } else {
    return `From ${formatDate(start)}`
  }
}

// Handle search
function handleSearch() {
  pagination.reset()
  loadServiceOrders()
}

// Clear search
function clearSearch() {
  searchQuery.value = ''
  statusFilter.value = ''
  pagination.reset()
  loadServiceOrders()
}

// View service order
function viewServiceOrder(serviceOrder: ServiceOrder) {
  selectedServiceOrder.value = serviceOrder
  showDetailModal.value = true
}

// Edit service order
function editServiceOrder(serviceOrder: ServiceOrder) {
  selectedServiceOrder.value = serviceOrder
  showEditModal.value = true
  showDetailModal.value = false
}

// Close modal
function closeModal() {
  showAddModal.value = false
  showEditModal.value = false
  selectedServiceOrder.value = null
}

// Handle service order saved
function handleServiceOrderSaved() {
  closeModal()
  loadServiceOrders()
}

// Download PDF
async function downloadPDF(serviceOrder: ServiceOrder) {
  try {
    const response = await ServiceOrderService.getServiceOrderPdf(serviceOrder.id)
    if (response.success && response.data?.data?.downloadUrl) {
      window.open(response.data.data.downloadUrl, '_blank')
    } else {
      alert('Failed to generate PDF: ' + (response.error || 'Unknown error'))
    }
  } catch (error) {
    console.error('Error downloading PDF:', error)
    alert('Error downloading PDF')
  }
}

// Delete service order
async function deleteServiceOrder(serviceOrder: ServiceOrder) {
  if (confirm(`Are you sure you want to delete service order ${serviceOrder.serviceOrderNo}?`)) {
    try {
      const response = await ServiceOrderService.deleteServiceOrder(serviceOrder.id)
      if (response.success) {
        loadServiceOrders()
      } else {
        alert('Failed to delete service order: ' + (response.error || 'Unknown error'))
      }
    } catch (error) {
      console.error('Error deleting service order:', error)
      alert('Error deleting service order')
    }
  }
}

// Watch for search query changes
watch(searchQuery, () => {
  if (searchQuery.value === '') {
    handleSearch()
  }
})

// Initialize
onMounted(() => {
  loadServiceOrders()
  loadCustomers()
})
</script>
